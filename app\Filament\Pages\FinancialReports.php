<?php

namespace App\Filament\Pages;

use App\Services\ReportingService;
use Carbon\Carbon;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Grid;
use Filament\Forms\Form;
use Filament\Pages\Page;
use Filament\Actions\Action;
use Filament\Support\Enums\MaxWidth;
use Illuminate\Contracts\Support\Htmlable;

class FinancialReports extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-chart-bar';
    protected static string $view = 'filament.pages.financial-reports';
    protected static ?string $navigationGroup = 'Reports';
    protected static ?int $navigationSort = 1;

    public ?string $startDate = null;
    public ?string $endDate = null;
    public ?string $reportType = 'summary';

    protected ReportingService $reportingService;

    public function boot(ReportingService $reportingService): void
    {
        $this->reportingService = $reportingService;
    }

    public function mount(): void
    {
        $this->startDate = Carbon::now()->startOfMonth()->format('Y-m-d');
        $this->endDate = Carbon::now()->format('Y-m-d');
    }

    public static function getNavigationLabel(): string
    {
        return __('Financial Reports');
    }

    public function getTitle(): string | Htmlable
    {
        return __('Financial Reports & Analytics');
    }

    public static function canAccess(): bool
    {
        return auth()->user()->can('viewFinancialReports', auth()->user());
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Grid::make(3)->schema([
                    DatePicker::make('startDate')
                        ->label('Start Date')
                        ->required()
                        ->default(Carbon::now()->startOfMonth())
                        ->maxDate(Carbon::now())
                        ->rules(['required', 'date', 'before_or_equal:endDate'])
                        ->validationAttribute('Start Date'),
                    DatePicker::make('endDate')
                        ->label('End Date')
                        ->required()
                        ->default(Carbon::now())
                        ->maxDate(Carbon::now())
                        ->rules(['required', 'date', 'after_or_equal:startDate'])
                        ->validationAttribute('End Date'),
                    Select::make('reportType')
                        ->label('Report Type')
                        ->options([
                            'summary' => 'Financial Summary',
                            'revenue_trends' => 'Revenue Trends',
                            'tax_summary' => 'Tax Summary',
                            'payment_methods' => 'Payment Methods',
                        ])
                        ->default('summary')
                        ->required()
                        ->rules(['required', 'in:summary,revenue_trends,tax_summary,payment_methods'])
                        ->validationAttribute('Report Type'),
                ]),
            ])
            ->statePath('data');
    }

    /**
     * Get validation rules for the form
     */
    protected function getValidationRules(): array
    {
        return [
            'data.startDate' => ['required', 'date', 'before_or_equal:data.endDate'],
            'data.endDate' => ['required', 'date', 'after_or_equal:data.startDate'],
            'data.reportType' => ['required', 'in:summary,revenue_trends,tax_summary,payment_methods'],
        ];
    }

    /**
     * Get validation attributes for better error messages
     */
    protected function getValidationAttributes(): array
    {
        return [
            'data.startDate' => 'Start Date',
            'data.endDate' => 'End Date',
            'data.reportType' => 'Report Type',
        ];
    }

    protected function getHeaderActions(): array
    {
        return [
            Action::make('generateReport')
                ->label('Generate Report')
                ->icon('heroicon-o-document-chart-bar')
                ->color('primary')
                ->action('generateReport'),
            Action::make('exportPDF')
                ->label('Export PDF')
                ->icon('heroicon-o-document-arrow-down')
                ->color('success')
                ->action('exportPDF'),
            Action::make('exportExcel')
                ->label('Export Excel')
                ->icon('heroicon-o-table-cells')
                ->color('warning')
                ->action('exportExcel'),
        ];
    }

    public function generateReport(): void
    {
        try {
            $this->validate();

            // Access form data through the data property
            $startDate = Carbon::parse($this->data['startDate']);
            $endDate = Carbon::parse($this->data['endDate']);
            $reportType = $this->data['reportType'];

            $this->dispatch('report-generated', [
                'reportType' => $reportType,
                'startDate' => $startDate->format('Y-m-d'),
                'endDate' => $endDate->format('Y-m-d'),
            ]);
        } catch (\Exception $e) {
            \Log::error('Financial Report generation error: ' . $e->getMessage());

            $this->addError('general', 'Failed to generate report: ' . $e->getMessage());
        }
    }

    public function exportPDF()
    {
        try {
            $this->validate();

            // Access form data through the data property
            $startDate = Carbon::parse($this->data['startDate']);
            $endDate = Carbon::parse($this->data['endDate']);

            return response()->streamDownload(function () use ($startDate, $endDate) {
                echo $this->generatePDFReport($startDate, $endDate);
            }, 'financial-report-' . $startDate->format('Y-m-d') . '-to-' . $endDate->format('Y-m-d') . '.pdf');
        } catch (\Exception $e) {
            \Log::error('Financial Report PDF export error: ' . $e->getMessage());

            $this->addError('general', 'Failed to export PDF: ' . $e->getMessage());
            return null;
        }
    }

    public function exportExcel()
    {
        try {
            if (!auth()->user()->can('exportReports', auth()->user())) {
                abort(403);
            }

            $this->validate();

            // Access form data through the data property
            $startDate = Carbon::parse($this->data['startDate']);
            $endDate = Carbon::parse($this->data['endDate']);

            $exportService = app(\App\Services\ExportService::class);
            return $exportService->exportFinancialReportExcel($startDate, $endDate);
        } catch (\Exception $e) {
            \Log::error('Financial Report Excel export error: ' . $e->getMessage());

            $this->addError('general', 'Failed to export Excel: ' . $e->getMessage());
            return null;
        }
    }

    public function getFinancialSummary(): array
    {
        try {
            // Access form data through the data property
            if (!isset($this->data['startDate']) || !isset($this->data['endDate'])) {
                return [];
            }

            $startDate = Carbon::parse($this->data['startDate']);
            $endDate = Carbon::parse($this->data['endDate']);

            return $this->reportingService->getFinancialSummary($startDate, $endDate);
        } catch (\Exception $e) {
            \Log::error('Financial Summary retrieval error: ' . $e->getMessage());
            return [];
        }
    }

    public function getRevenueTrends(): array
    {
        if (!$this->startDate || !$this->endDate) {
            return [];
        }

        $startDate = Carbon::parse($this->startDate);
        $endDate = Carbon::parse($this->endDate);

        return $this->reportingService->getRevenueTrends($startDate, $endDate, 'daily');
    }

    public function getTaxSummary(): array
    {
        if (!$this->startDate || !$this->endDate) {
            return [];
        }

        $startDate = Carbon::parse($this->startDate);
        $endDate = Carbon::parse($this->endDate);

        return $this->reportingService->getTaxSummary($startDate, $endDate);
    }

    public function getPaymentMethodAnalysis(): array
    {
        if (!$this->startDate || !$this->endDate) {
            return [];
        }

        $startDate = Carbon::parse($this->startDate);
        $endDate = Carbon::parse($this->endDate);

        return $this->reportingService->getPaymentMethodAnalysis($startDate, $endDate)->toArray();
    }

    private function generatePDFReport(Carbon $startDate, Carbon $endDate): string
    {
        $data = [
            'summary' => $this->reportingService->getFinancialSummary($startDate, $endDate),
            'revenue_trends' => $this->reportingService->getRevenueTrends($startDate, $endDate),
            'tax_summary' => $this->reportingService->getTaxSummary($startDate, $endDate),
            'payment_methods' => $this->reportingService->getPaymentMethodAnalysis($startDate, $endDate),
            'start_date' => $startDate->format('Y-m-d'),
            'end_date' => $endDate->format('Y-m-d'),
            'generated_at' => Carbon::now()->format('Y-m-d H:i:s'),
        ];

        $pdf = app('dompdf.wrapper');
        $pdf->loadView('reports.financial-report-pdf', $data);
        
        return $pdf->output();
    }

    public function getMaxContentWidth(): MaxWidth
    {
        return MaxWidth::Full;
    }
}
